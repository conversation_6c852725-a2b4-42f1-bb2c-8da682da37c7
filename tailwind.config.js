/** @type {import('tailwindcss').Config} */
export default {
    content: [
        "./resources/**/*.blade.php",
        "./resources/**/*.jsx",
        "./resources/**/*.js",
        "./resources/js/v2/**/*.jsx",  // specifically include v2 directory
        './packages/apimio/mapping-fields-package/resources/js/**/*.{js,jsx,ts,tsx,vue}',
        "./packages/**/resources/**/*.{js,jsx,blade.php}",
    ],
    theme: {
        extend: {
            screens: {
                'md2': '1000px',
                '2xl2': { 'min': '1920px' },
            },
        },
    },
    plugins: [],
    corePlugins: {
        preflight: false, // This prevents Tailwind from conflicting with Ant Design
    },
};
