import React from "react";
import { Form, Select, Button, message } from "antd";
import { ExclamationCircleOutlined, DeleteOutlined } from "@ant-design/icons";
import { FormulaCallingComponent } from "./FormulaCallingComponent";
import SelectAttribute from "./SelectAttribute";
import "../../css/mapping-styles.css";

const Formulas = [
  { value: "assign", label: "Assign" },
  { value: "split", label: "Split" },
  { value: "merge", label: "Merge Basic" },
  { value: "short_code", label: "Merge Advance" },
  { value: "replace", label: "Replace" },
  { value: "slug", label: "Slug" },
  { value: "vlookup", label: "VLookup" },
  { value: "calculate", label: "Calculate" },
  { value: "expand", label: "Expand" },
];

const MappingRow = ({
  index,
  node,
  convertedInputArray = [],
  convertedOutputArray = [],
  onDelete,
  onFormulaChange,
  onFromFieldChange,
  onFieldChange,
  onCreateAttribute,
}) => {
  //  const [selectedFormula, setSelectedFormula] = useState(node?.with_formula || "assign");
  const selectedFormula = node.with_formula || "assign";
  const checkIfMapped = (node) => {
  // Helper function to check if a value is valid (not null, undefined, or empty string)
  const isValidValue = (value) => value !== null && value !== undefined && value !== "";

  switch (node.with_formula) {
    case "split":
      // The row is mapped if:
      // 1. `from` has a valid value at index 0
      // 2. Both `to[0]` and `to[1]` have valid selections.
      // 3. Separator (with) has a value.
      return (
        isValidValue(node?.from?.[0]) &&
        isValidValue(node?.to?.[0]) &&
        isValidValue(node?.to?.[1]) &&
        node?.with && node?.with.trim() !== "" // Check if separator is filled
      );
    case "merge":
      // The row is mapped if:
      // 1. `from` has valid values at index 0 and 1.
      // 2. `to` has a valid value at index 0.
      // 3. Glue (with) should be filled.
      return (
        isValidValue(node?.from?.[0]) &&
        isValidValue(node?.from?.[1]) &&
        isValidValue(node?.to?.[0]) &&
        node?.with && node?.with.trim() !== "" // Check if glue is filled
      );
    case "short_code":
      // The row is mapped if:
      // 1. `from` has 1 item with content.
      // 2. `to` has 1 item.
      return (
        isValidValue(node?.from?.[0]) && node?.from?.[0].trim() !== "" &&
        isValidValue(node?.to?.[0])
      );
    case "replace":
      // The row is mapped if:
      // 1. `from` has 1 item.
      // 2. `to` has 1 item.
      // 3. `replace` and `with` should be filled.
      return (
        isValidValue(node?.from?.[0]) &&
        isValidValue(node?.to?.[0]) &&
        node?.replace && node?.replace.trim() !== "" &&
        node?.with && node?.with.trim() !== ""
      );
    case "expand":
      // The row is mapped if:
      // 1. `from` has 1 item.
      // 2. `to` has 1 item.
      // 3. `with` should be filled.
      return (
        isValidValue(node?.from?.[0]) &&
        isValidValue(node?.to?.[0]) &&
        node?.with && node?.with.trim() !== "" // Ensure `with` is filled
      );
    case "calculate":
      // Similar to expand, calculate also requires `with` and `to`
      return (
        isValidValue(node?.from?.[0]) &&
        isValidValue(node?.to?.[0]) &&
        node?.with && node?.with.trim() !== ""
      );
    case "vlookup":
      // The row is mapped if:
      // 1. `from` has 1 item (source value to look up)
      // 2. `to` has 1 item (destination field)
      // 3. `with` has a value (the lookup table ID)
      return (
        isValidValue(node?.from?.[0]) &&
        isValidValue(node?.to?.[0]) &&
        node?.with && node?.with.trim() !== ""
      );
    case "slug":
      // The row is mapped if:
      // 1. `from` has 1 item.
      // 2. `to` has 1 item.
      return isValidValue(node?.from?.[0]) && isValidValue(node?.to?.[0]);
    case "assign":
      // The row is mapped if:
      // 1. `from` has 1 item.
      // 2. `to` has 1 item.
      return isValidValue(node?.from?.[0]) && isValidValue(node?.to?.[0]);
    default:
      return false;
  }
};


  const isMapped = checkIfMapped(node);

  const handleFromChange = (value) => {
    onFromFieldChange(node.id, value);
  };
  const handleFormulaChange = (value) => {
    onFormulaChange(node.id, value);
  };

  const HandleFieldChange = (rowId, newValue) => {
    onFieldChange(rowId, newValue);
  };
  const handleDelete = () => {
    onDelete(node.id);
     message.success("Row deleted successfully");
  };

  const FormulaComponent = FormulaCallingComponent[selectedFormula] || null;

  return (
    <div
      id={node.id}
      className={` px-4 py-3 justify-content-between border-l-8 border-t border-r border-b
        ${
          isMapped ? "border-green-500" : "border-yellow-500"
        }
        rounded-md shadow-md
      `}
    >
      <Button
        type="text"
        icon={<DeleteOutlined style={{ color: "#ff4d4f", fontSize: "16px" }} />}
        className="absolute top-2 right-2 z-50"
        style={{
          minWidth: "32px",
          minHeight: "32px",
          padding: "4px",
          pointerEvents: "auto",
          zIndex: 50
        }}
        onClick={handleDelete} // attach your delete handler here
      />
      <div className="flex justify-start gap-6" style={{ paddingRight: "48px" }}>
        {selectedFormula !== "short_code" && (
          <div className="flex flex-col gap-1 mb-0">
            {/*<label className="font-semibold">CSV Attribute</label>*/}

            <Form.Item
              key={index}
              label={`CSV Attribute`}
              //name={`nodes[data][${index}][from][]`} // Dynamic name handling
              style={{ marginBottom: "0" }}
            >
              <SelectAttribute
                options={convertedInputArray}
                value={node?.from[0]} // or node?.from if multi
                onChange={handleFromChange}
              />
            </Form.Item>
          </div>
        )}
        <div className="flex flex-col gap-1">
          {/*<label className="font-semibold">Formulas</label>*/}
          <Form.Item
            key={index}
            label={`Formulas`}
            // name={`nodes[data][${index}][with_formula]`} // Dynamic name handling
            style={{ marginBottom: "0" }}
          >
            <Select
              style={{
                width: 180,
              }}
              value={selectedFormula} // controlled
              onChange={handleFormulaChange}
              options={Formulas}
            />
          </Form.Item>
        </div>
        {FormulaComponent && (
          <div className="flex-1" style={{ maxWidth: "calc(100% - 48px)" }}>
            <FormulaComponent
              rowIndex={index}
              node={node}
              onFieldChange={HandleFieldChange}
              convertedOutputArray={convertedOutputArray}
              convertedInputArray={convertedInputArray}
            />
          </div>
        )}
      </div>
      {!isMapped ? (
        <div className="flex justify-between items-center mt-2">
          <p className="flex items-center text-xs text-yellow-500">
            <ExclamationCircleOutlined
              style={{ color: "#F59E0B", fontSize: "15px", marginRight: "4px" }}
            />
            Please map all required fields for this row.
          </p>
          <Button
            type="text"
            style={{ color: "#740898", backgroundColor: "transparent" }}
            onClick={() => onCreateAttribute(node.id)}
          >
            Create Apimio Attribute
          </Button>
        </div>
      ) : (
        // Add equivalent spacing for mapped rows to maintain consistent height
        <div className="mt-2" style={{ height: "8px" }}></div>
      )}
    </div>
  );
};

export default MappingRow;
