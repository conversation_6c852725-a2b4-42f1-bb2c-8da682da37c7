@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mapping-specific styles to ensure Tailwind CSS classes work properly */

/* Ensure border utilities work correctly */
.border-l-8 {
    border-left-width: 8px !important;
}

.border-green-500 {
    border-color: #10b981 !important;
}

.border-yellow-500 {
    border-color: #f59e0b !important;
}

/* Ensure flex utilities work correctly */
.flex {
    display: flex !important;
}

.flex-col {
    flex-direction: column !important;
}

.justify-start {
    justify-content: flex-start !important;
}

.justify-between {
    justify-content: space-between !important;
}

.items-center {
    align-items: center !important;
}

.gap-1 {
    gap: 0.25rem !important;
}

.gap-2 {
    gap: 0.5rem !important;
}

.gap-4 {
    gap: 1rem !important;
}

.gap-6 {
    gap: 1.5rem !important;
}

/* Ensure spacing utilities work correctly */
.px-4 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

.py-3 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
}

.pt-4 {
    padding-top: 1rem !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-1 {
    margin-bottom: 0.25rem !important;
}

.mt-2 {
    margin-top: 0.5rem !important;
}

.ml-4 {
    margin-left: 1rem !important;
}

/* Ensure positioning utilities work correctly */
.absolute {
    position: absolute !important;
}

.relative {
    position: relative !important;
}

.top-2 {
    top: 0.5rem !important;
}

.right-2 {
    right: 0.5rem !important;
}

.z-50 {
    z-index: 50 !important;
}

/* Ensure border radius utilities work correctly */
.rounded-md {
    border-radius: 0.375rem !important;
}

.rounded-lg {
    border-radius: 0.5rem !important;
}

/* Ensure shadow utilities work correctly */
.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

/* Ensure text utilities work correctly */
.text-xs {
    font-size: 0.75rem !important;
    line-height: 1rem !important;
}

.text-sm {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
}

.text-yellow-500 {
    color: #f59e0b !important;
}

.text-red-500 {
    color: #ef4444 !important;
}

.text-white {
    color: #ffffff !important;
}

.text-gray-600 {
    color: #4b5563 !important;
}

.text-gray-500 {
    color: #6b7280 !important;
}

.text-gray-200 {
    color: #e5e7eb !important;
}

.text-blue-500 {
    color: #3b82f6 !important;
}

/* Ensure background utilities work correctly */
.bg-white {
    background-color: #ffffff !important;
}

.bg-gray-50 {
    background-color: #f9fafb !important;
}

.bg-gray-100 {
    background-color: #f3f4f6 !important;
}

.bg-blue-600 {
    background-color: #2563eb !important;
}

.bg-blue-700 {
    background-color: #1d4ed8 !important;
}

/* Ensure border utilities work correctly */
.border {
    border-width: 1px !important;
}

.border-t {
    border-top-width: 1px !important;
}

.border-r {
    border-right-width: 1px !important;
}

.border-b {
    border-bottom-width: 1px !important;
}

.border-gray-200 {
    border-color: #e5e7eb !important;
}

.border-gray-300 {
    border-color: #d1d5db !important;
}

/* Ensure flex utilities work correctly */
.flex-1 {
    flex: 1 1 0% !important;
}

/* Ensure width utilities work correctly */
.w-full {
    width: 100% !important;
}

.w-3 {
    width: 0.75rem !important;
}

.w-56 {
    width: 14rem !important;
}

/* Ensure height utilities work correctly */
.h-3 {
    height: 0.75rem !important;
}

.min-h-\[80px\] {
    min-height: 80px !important;
}

.max-h-\[100px\] {
    max-height: 100px !important;
}

.max-h-48 {
    max-height: 12rem !important;
}

/* Ensure display utilities work correctly */
.hidden {
    display: none !important;
}

/* Ensure position utilities work correctly */
.fixed {
    position: fixed !important;
}

.left-0 {
    left: 0 !important;
}

/* Ensure z-index utilities work correctly */
.z-10 {
    z-index: 10 !important;
}

/* Ensure overflow utilities work correctly */
.overflow-y-auto {
    overflow-y: auto !important;
}

/* Ensure spacing utilities work correctly */
.p-2 {
    padding: 0.5rem !important;
}

.px-2 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
}

.px-3 {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
}

.py-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
}

.mt-1 {
    margin-top: 0.25rem !important;
}

/* Ensure focus utilities work correctly */
.focus\:outline-none:focus {
    outline: 2px solid transparent !important;
    outline-offset: 2px !important;
}

.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
}

.focus\:ring-blue-500:focus {
    --tw-ring-opacity: 1 !important;
    --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity)) !important;
}

/* Ensure hover utilities work correctly */
.hover\:text-red-500:hover {
    color: #ef4444 !important;
}

.hover\:bg-gray-50:hover {
    background-color: #f9fafb !important;
}

.hover\:bg-gray-100:hover {
    background-color: #f3f4f6 !important;
}

.hover\:bg-blue-700:hover {
    background-color: #1d4ed8 !important;
}

/* Ensure font utilities work correctly */
.font-semibold {
    font-weight: 600 !important;
}

.font-normal {
    font-weight: 400 !important;
}

.uppercase {
    text-transform: uppercase !important;
}

/* Ensure border utilities work correctly */
.last\:border-0:last-child {
    border-width: 0px !important;
}

/* Ensure space utilities work correctly */
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0 !important;
    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse))) !important;
    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse)) !important;
}
