@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mapping-specific styles to ensure Tailwind CSS classes work properly */

/* Ensure border utilities work correctly */
.border-l-8 {
    border-left-width: 8px !important;
}

.border-green-500 {
    border-color: #10b981 !important;
}

.border-yellow-500 {
    border-color: #f59e0b !important;
}

/* Ensure flex utilities work correctly */
.flex {
    display: flex !important;
}

.flex-col {
    flex-direction: column !important;
}

.justify-start {
    justify-content: flex-start !important;
}

.justify-between {
    justify-content: space-between !important;
}

.items-center {
    align-items: center !important;
}

.gap-1 {
    gap: 0.25rem !important;
}

.gap-6 {
    gap: 1.5rem !important;
}

/* Ensure spacing utilities work correctly */
.px-4 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

.py-3 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mt-2 {
    margin-top: 0.5rem !important;
}

.ml-4 {
    margin-left: 1rem !important;
}

/* Ensure positioning utilities work correctly */
.absolute {
    position: absolute !important;
}

.relative {
    position: relative !important;
}

.top-2 {
    top: 0.5rem !important;
}

.right-2 {
    right: 0.5rem !important;
}

.z-50 {
    z-index: 50 !important;
}

/* Ensure border radius utilities work correctly */
.rounded-md {
    border-radius: 0.375rem !important;
}

/* Ensure shadow utilities work correctly */
.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Ensure text utilities work correctly */
.text-xs {
    font-size: 0.75rem !important;
    line-height: 1rem !important;
}

.text-yellow-500 {
    color: #f59e0b !important;
}

.text-red-500 {
    color: #ef4444 !important;
}

.text-white {
    color: #ffffff !important;
}

/* Ensure background utilities work correctly */
.bg-\[#740898\] {
    background-color: #740898 !important;
}

/* Ensure border utilities work correctly */
.border {
    border-width: 1px !important;
}

.border-t {
    border-top-width: 1px !important;
}

.border-r {
    border-right-width: 1px !important;
}

.border-b {
    border-bottom-width: 1px !important;
}

.border-\[#740898\] {
    border-color: #740898 !important;
}

.rounded-\[4px\] {
    border-radius: 4px !important;
}

/* Ensure flex utilities work correctly */
.flex-1 {
    flex: 1 1 0% !important;
}

/* Ensure width utilities work correctly */
.w-screen {
    width: 100vw !important;
}

/* Ensure height utilities work correctly */
.h-screen {
    height: 100vh !important;
}

/* Ensure display utilities work correctly */
.hidden {
    display: none !important;
}

/* Ensure position utilities work correctly */
.fixed {
    position: fixed !important;
}

.left-0 {
    left: 0 !important;
}

/* Ensure z-index utilities work correctly */
.z-\[99\] {
    z-index: 99 !important;
}

/* Additional utility classes that might be missing */
.justify-content-between {
    justify-content: space-between !important;
}

/* Ensure all Tailwind utilities are available with proper specificity */
